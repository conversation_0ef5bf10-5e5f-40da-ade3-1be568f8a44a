@extends('layouts.app')

@section('title', 'Role Management - Admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">Role Management</h1>
                <p class="text-gray-400 mt-2">Manage system roles and permissions</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('admin.roles.statistics') }}" class="btn-secondary">
                    <i class="fas fa-chart-bar mr-2"></i>Statistics
                </a>
                <a href="{{ route('admin.roles.create') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Add Role
                </a>
                <a href="{{ route('admin.dashboard') }}" class="btn-secondary">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <i class="fas fa-user-shield text-blue-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Total Roles</p>
                    <p class="text-white text-2xl font-bold">{{ $totalRoles ?? 0 }}</p>
                </div>
            </div>
        </div>
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <i class="fas fa-key text-green-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Total Permissions</p>
                    <p class="text-white text-2xl font-bold">{{ $totalPermissions ?? 0 }}</p>
                </div>
            </div>
        </div>
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <i class="fas fa-users-cog text-purple-400 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-gray-400 text-sm">Active Roles</p>
                    <p class="text-white text-2xl font-bold">{{ $activeRoles ?? 0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="dark-card rounded-lg shadow-lg p-6 mb-6">
        <form method="GET" action="{{ route('admin.roles.index') }}" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Search</label>
                <input type="text" name="search" value="{{ request('search') }}" placeholder="Role name..." class="dark-input">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                <select name="status" class="dark-input">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <div class="flex items-end space-x-2">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
                <a href="{{ route('admin.roles.index') }}" class="btn-secondary">
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Roles Table -->
    <div class="dark-card rounded-lg shadow-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-700">
                <thead class="bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Permissions</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Users</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-gray-900 divide-y divide-gray-700">
                    @forelse($roles ?? [] as $role)
                        <tr class="hover:bg-gray-800 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center">
                                            <i class="fas fa-user-shield text-white"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-white">{{ $role->name ?? 'N/A' }}</div>
                                        <div class="text-sm text-gray-400">{{ $role->display_name ?? $role->name ?? 'N/A' }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-wrap gap-1">
                                    @if(isset($role->permissions) && $role->permissions->count() > 0)
                                        @foreach($role->permissions->take(3) as $permission)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300">
                                                {{ $permission->name }}
                                            </span>
                                        @endforeach
                                        @if($role->permissions->count() > 3)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-600 text-gray-300">
                                                +{{ $role->permissions->count() - 3 }} more
                                            </span>
                                        @endif
                                    @else
                                        <span class="text-gray-400 text-sm">No permissions</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-white">{{ $role->users_count ?? 0 }}</div>
                                <div class="text-sm text-gray-400">users</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ ($role->status ?? 'active') === 'active' ? 'bg-green-900 text-green-200' : 'bg-red-900 text-red-200' }}">
                                    {{ ucfirst($role->status ?? 'active') }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                {{ isset($role->created_at) ? $role->created_at->format('M d, Y') : 'N/A' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.roles.show', $role->id ?? 1) }}" class="text-blue-400 hover:text-blue-300">View</a>
                                    <a href="{{ route('admin.roles.edit', $role->id ?? 1) }}" class="text-yellow-400 hover:text-yellow-300">Edit</a>
                                    @if(!in_array($role->name ?? '', ['admin', 'super-admin']))
                                        <form method="POST" action="{{ route('admin.roles.toggle-status', $role->id ?? 1) }}" class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="{{ ($role->status ?? 'active') === 'active' ? 'text-red-400 hover:text-red-300' : 'text-green-400 hover:text-green-300' }}">
                                                {{ ($role->status ?? 'active') === 'active' ? 'Disable' : 'Enable' }}
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ route('admin.roles.destroy', $role->id ?? 1) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this role?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-400 hover:text-red-300">Delete</button>
                                        </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-400">
                                No roles found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    @if(isset($roles) && $roles->hasPages())
        <div class="dark-card px-4 py-3 flex items-center justify-between border-t border-gray-700 sm:px-6 mt-6">
            <div class="flex-1 flex justify-between sm:hidden">
                @if($roles->onFirstPage())
                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-400 bg-gray-800 cursor-default">
                        Previous
                    </span>
                @else
                    <a href="{{ $roles->previousPageUrl() }}" class="relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-700">
                        Previous
                    </a>
                @endif

                @if($roles->hasMorePages())
                    <a href="{{ $roles->nextPageUrl() }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-700">
                        Next
                    </a>
                @else
                    <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-400 bg-gray-800 cursor-default">
                        Next
                    </span>
                @endif
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-300">
                        Showing
                        <span class="font-medium text-white">{{ $roles->firstItem() }}</span>
                        to
                        <span class="font-medium text-white">{{ $roles->lastItem() }}</span>
                        of
                        <span class="font-medium text-white">{{ $roles->total() }}</span>
                        results
                    </p>
                </div>
                <div>
                    {{ $roles->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    @endif
</div>
@endsection