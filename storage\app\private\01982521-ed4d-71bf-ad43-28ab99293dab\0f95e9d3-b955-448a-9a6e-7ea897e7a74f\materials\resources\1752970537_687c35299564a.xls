<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>Data Export Indah Berkah Abadi</Title>
  <Author>Indah Berkah Abadi</Author>
  <Created>2025-06-20T02:22:44.265797Z</Created>
 </DocumentProperties>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>12000</WindowHeight>
  <WindowWidth>15000</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s62">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="16" ss:Color="#FFFFFF" ss:Bold="1"/>
   <Interior ss:Color="#1E40AF" ss:Pattern="Solid"/>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="s63">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="12" ss:Color="#FFFFFF" ss:Bold="1"/>
   <Interior ss:Color="#3B82F6" ss:Pattern="Solid"/>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="s64">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Bold="1"/>
   <Interior ss:Color="#F3F4F6" ss:Pattern="Solid"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
  </Style>
  <Style ss:ID="s65">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
  </Style>
  <Style ss:ID="s66">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Right"/>
  </Style>
  <Style ss:ID="s67">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center"/>
  </Style>
 </Styles><Worksheet ss:Name="Data Distribusi">
  <Table><Row>
   <Cell ss:MergeAcross="9" ss:StyleID="s62"><Data ss:Type="String">DATA DISTRIBUSI - INDAH BERKAH ABADI</Data></Cell>
  </Row><Row>
   <Cell ss:MergeAcross="9" ss:StyleID="s62"><Data ss:Type="String">Periode: 01/06/2025 - 30/06/2025</Data></Cell>
  </Row><Row>
   <Cell ss:MergeAcross="9" ss:StyleID="s62"><Data ss:Type="String">Diunduh pada: 20/06/2025 10:22:44 Asia/Makassar</Data></Cell>
  </Row>
  <Row></Row><Row><Cell ss:StyleID="s64"><Data ss:Type="String">No</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Tanggal Distribusi</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Produk</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Toko</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Lokasi</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Jumlah</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Jumlah Diterima</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Status</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Catatan</Data></Cell><Cell ss:StyleID="s64"><Data ss:Type="String">Dibuat Pada</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">1</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">18/06/2025</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Smartphone Android Premium</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Toko Jakarta</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Jakarta Pusat</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">12</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">12</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Dikonfirmasi</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Ada sedikit kerusakan pada kemasan</Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row><Row><Cell ss:StyleID="s67"><Data ss:Type="Number">2</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">18/06/2025</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Monitor LED 24 inch</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Toko Jakarta</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Jakarta Pusat</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">38</Data></Cell><Cell ss:StyleID="s66"><Data ss:Type="Number">38</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String">Dikonfirmasi</Data></Cell><Cell ss:StyleID="s65"><Data ss:Type="String"></Data></Cell><Cell ss:StyleID="s67"><Data ss:Type="String">19/06/2025 16:02:50</Data></Cell></Row></Table>
 </Worksheet></Workbook>