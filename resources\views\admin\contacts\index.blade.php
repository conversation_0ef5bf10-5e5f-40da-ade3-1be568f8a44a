@extends('layouts.app')

@section('title', 'Contact Messages - Admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">Contact Messages</h1>
                <p class="text-gray-300 mt-2">Manage customer inquiries and support requests</p>
            </div>
            <a href="{{ route('admin.dashboard') }}" class="btn-secondary">
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
        <div class="dark-card rounded-lg shadow-lg p-4 card-hover">
            <div class="text-2xl font-bold text-white">{{ number_format($stats['total']) }}</div>
            <div class="text-sm text-gray-400">Total Messages</div>
        </div>
        <div class="dark-card rounded-lg shadow-lg p-4 card-hover">
            <div class="text-2xl font-bold text-red-400">{{ number_format($stats['new']) }}</div>
            <div class="text-sm text-gray-400">New Messages</div>
        </div>
        <div class="dark-card rounded-lg shadow-lg p-4 card-hover">
            <div class="text-2xl font-bold text-yellow-400">{{ number_format($stats['read']) }}</div>
            <div class="text-sm text-gray-400">Read Messages</div>
        </div>
        <div class="dark-card rounded-lg shadow-lg p-4 card-hover">
            <div class="text-2xl font-bold text-blue-400">{{ number_format($stats['replied']) }}</div>
            <div class="text-sm text-gray-400">Replied</div>
        </div>
        <div class="dark-card rounded-lg shadow-lg p-4 card-hover">
            <div class="text-2xl font-bold text-green-400">{{ number_format($stats['closed']) }}</div>
            <div class="text-sm text-gray-400">Closed</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="dark-card rounded-lg shadow-lg mb-6">
        <div class="p-6">
            <form method="GET" action="{{ route('admin.contacts.index') }}" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-64">
                    <input type="text" name="search" value="{{ request('search') }}" placeholder="Search by name, email, subject, or message..." class="w-full dark-input form-input">
                </div>
                <div>
                    <select name="status" class="dark-input form-input">
                        <option value="">All Statuses</option>
                        <option value="new" {{ request('status') === 'new' ? 'selected' : '' }}>New</option>
                        <option value="read" {{ request('status') === 'read' ? 'selected' : '' }}>Read</option>
                        <option value="replied" {{ request('status') === 'replied' ? 'selected' : '' }}>Replied</option>
                        <option value="closed" {{ request('status') === 'closed' ? 'selected' : '' }}>Closed</option>
                    </select>
                </div>
                <button type="submit" class="btn-primary">
                    Filter
                </button>
                @if(request()->hasAny(['search', 'status']))
                    <a href="{{ route('admin.contacts.index') }}" class="btn-secondary">
                        Clear
                    </a>
                @endif
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="dark-card rounded-lg shadow-lg mb-6">
        <div class="p-4">
            <form id="bulk-action-form" method="POST" action="{{ route('admin.contacts.bulk-update') }}">
                @csrf
                <div class="flex items-center gap-4">
                    <label class="flex items-center text-white">
                        <input type="checkbox" id="select-all" class="mr-2 text-red-500 focus:ring-red-500">
                        Select All
                    </label>
                    <select name="action" class="dark-input">
                        <option value="">Bulk Actions</option>
                        <option value="mark_read">Mark as Read</option>
                        <option value="mark_replied">Mark as Replied</option>
                        <option value="mark_closed">Mark as Closed</option>
                        <option value="delete">Delete</option>
                    </select>
                    <button type="submit" class="btn-primary" onclick="return confirm('Are you sure you want to perform this action?')">
                        Apply
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Contacts Table -->
    <div class="dark-card rounded-lg shadow-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-700">
                <thead class="bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                            <input type="checkbox" id="select-all-header" class="mr-2 text-red-500 focus:ring-red-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Contact</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Subject</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-gray-900 divide-y divide-gray-700">
                    @forelse($contacts as $contact)
                        <tr class="hover:bg-gray-800 {{ $contact->status === 'new' ? 'bg-red-900 bg-opacity-20' : '' }} transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="contact_ids[]" value="{{ $contact->id }}" class="contact-checkbox text-red-500 focus:ring-red-500" form="bulk-action-form">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-white">{{ $contact->name }}</div>
                                    <div class="text-sm text-gray-400">{{ $contact->email }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-white">{{ Str::limit($contact->subject, 50) }}</div>
                                <div class="text-sm text-gray-400">{{ Str::limit($contact->message, 100) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $contact->status_badge_color }}">
                                    {{ $contact->formatted_status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                <div>{{ $contact->created_at->format('M d, Y') }}</div>
                                <div>{{ $contact->created_at->format('h:i A') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.contacts.show', $contact) }}" class="text-blue-400 hover:text-blue-300">View</a>
                                    <form method="POST" action="{{ route('admin.contacts.destroy', $contact) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this contact?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-400 hover:text-red-300">Delete</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-400">
                                No contact messages found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    @if($contacts->hasPages())
        <div class="dark-card px-4 py-3 flex items-center justify-between border-t border-gray-700 sm:px-6 mt-6">
            <div class="flex-1 flex justify-between sm:hidden">
                @if($contacts->onFirstPage())
                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-400 bg-gray-800 cursor-default">
                        Previous
                    </span>
                @else
                    <a href="{{ $contacts->previousPageUrl() }}" class="relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-700">
                        Previous
                    </a>
                @endif

                @if($contacts->hasMorePages())
                    <a href="{{ $contacts->nextPageUrl() }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-700">
                        Next
                    </a>
                @else
                    <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-400 bg-gray-800 cursor-default">
                        Next
                    </span>
                @endif
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-300">
                        Showing
                        <span class="font-medium text-white">{{ $contacts->firstItem() }}</span>
                        to
                        <span class="font-medium text-white">{{ $contacts->lastItem() }}</span>
                        of
                        <span class="font-medium text-white">{{ $contacts->total() }}</span>
                        results
                    </p>
                </div>
                <div>
                    {{ $contacts->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    @endif
</div>

<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

document.getElementById('select-all-header').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    document.getElementById('select-all').checked = this.checked;
});
</script>
@endsection