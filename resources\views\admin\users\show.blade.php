@extends('layouts.app')

@section('title', 'User Details - Admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">User Details</h1>
                <p class="text-gray-400 mt-2">View detailed information about {{ $user->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.users.edit', $user) }}" class="btn-primary">
                    <i class="fas fa-edit mr-2"></i>Edit User
                </a>
                <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Users
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- User Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Basic Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                            <p class="text-white bg-gray-800 border border-gray-700 rounded-md px-3 py-2">{{ $user->name }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                            <p class="text-white bg-gray-800 border border-gray-700 rounded-md px-3 py-2">{{ $user->email }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                            <p class="text-white bg-gray-800 border border-gray-700 rounded-md px-3 py-2">{{ $user->phone ?: 'Not provided' }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $user->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ ucfirst($user->status) }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Email Verified</label>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $user->email_verified_at ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ $user->email_verified_at ? 'Verified' : 'Unverified' }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Member Since</label>
                            <p class="text-white bg-gray-800 border border-gray-700 rounded-md px-3 py-2">{{ $user->created_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Roles and Permissions -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Roles & Permissions</h2>
                    
                    @if($user->roles->count() > 0)
                        <div class="space-y-4">
                            @foreach($user->roles as $role)
                                <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                                    <div class="flex justify-between items-start mb-3">
                                        <div>
                                            <h3 class="text-lg font-medium text-white">{{ $role->name }}</h3>
                                            @if($role->description)
                                                <p class="text-gray-400 text-sm">{{ $role->description }}</p>
                                            @endif
                                        </div>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $role->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ ucfirst($role->status) }}
                                        </span>
                                    </div>
                                    
                                    @if($role->permissions->count() > 0)
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-300 mb-2">Permissions:</h4>
                                            <div class="flex flex-wrap gap-2">
                                                @foreach($role->permissions as $permission)
                                                    <span class="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                                                        {{ $permission->name }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-user-slash text-gray-500 text-4xl mb-4"></i>
                            <p class="text-gray-400">No roles assigned to this user</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Quick Stats</h2>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Total Roles</span>
                            <span class="text-white font-semibold">{{ $user->roles->count() }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Last Login</span>
                            <span class="text-white font-semibold">{{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never' }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Account Age</span>
                            <span class="text-white font-semibold">{{ $user->created_at->diffForHumans() }}</span>
                        </div>
                        
                        @if($user->email_verified_at)
                            <div class="flex justify-between items-center">
                                <span class="text-gray-400">Email Verified</span>
                                <span class="text-white font-semibold">{{ $user->email_verified_at->diffForHumans() }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Quick Actions</h2>
                    <div class="space-y-3">
                        <a href="{{ route('admin.users.edit', $user) }}" class="w-full btn-primary text-center">
                            <i class="fas fa-edit mr-2"></i>Edit User
                        </a>
                        
                        @if(!$user->email_verified_at)
                            <form method="POST" action="{{ route('admin.users.verify-email', $user) }}" class="w-full">
                                @csrf
                                <button type="submit" class="w-full btn-success">
                                    <i class="fas fa-check-circle mr-2"></i>Verify Email
                                </button>
                            </form>
                        @endif
                        
                        <form method="POST" action="{{ route('admin.users.reset-password', $user) }}" class="w-full">
                            @csrf
                            <button type="submit" class="w-full btn-warning" onclick="return confirm('Send password reset email to this user?')">
                                <i class="fas fa-key mr-2"></i>Reset Password
                            </button>
                        </form>
                        
                        @if($user->id !== auth()->id())
                            <form method="POST" action="{{ route('admin.users.destroy', $user) }}" class="w-full">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full btn-danger" onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                    <i class="fas fa-trash mr-2"></i>Delete User
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Activity Log -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Recent Activity</h2>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                            <div class="flex-1">
                                <p class="text-white text-sm">Account created</p>
                                <p class="text-gray-400 text-xs">{{ $user->created_at->format('M d, Y \\a\\t h:i A') }}</p>
                            </div>
                        </div>
                        
                        @if($user->email_verified_at)
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                                <div class="flex-1">
                                    <p class="text-white text-sm">Email verified</p>
                                    <p class="text-gray-400 text-xs">{{ $user->email_verified_at->format('M d, Y \\a\\t h:i A') }}</p>
                                </div>
                            </div>
                        @endif
                        
                        @if($user->last_login_at)
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                <div class="flex-1">
                                    <p class="text-white text-sm">Last login</p>
                                    <p class="text-gray-400 text-xs">{{ $user->last_login_at->format('M d, Y \\a\\t h:i A') }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection