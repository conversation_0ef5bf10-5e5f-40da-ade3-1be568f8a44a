<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->isAdmin() && !auth()->user()->isSuperAdmin()) {
                abort(403, 'Access denied. Administrator privileges required.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of contacts.
     */
    public function index(Request $request)
    {
        $query = Contact::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $contacts = $query->latest()->paginate(20);

        // Get statistics
        $stats = [
            'total' => Contact::count(),
            'new' => Contact::where('status', 'new')->count(),
            'read' => Contact::where('status', 'read')->count(),
            'replied' => Contact::where('status', 'replied')->count(),
            'closed' => Contact::where('status', 'closed')->count(),
        ];

        return view('admin.contacts.index', compact('contacts', 'stats'));
    }

    /**
     * Display the specified contact.
     */
    public function show(Contact $contact)
    {
        // Mark as read if it's new
        if ($contact->status === 'new') {
            $contact->markAsRead(auth()->id());
        }

        return view('admin.contacts.show', compact('contact'));
    }

    /**
     * Update the specified contact status.
     */
    public function update(Request $request, Contact $contact)
    {
        $request->validate([
            'status' => 'required|in:' . implode(',', [Contact::STATUS_NEW, Contact::STATUS_READ, Contact::STATUS_REPLIED, Contact::STATUS_CLOSED]),
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        $contact->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes
        ]);

        return back()->with('success', 'Contact updated successfully.');
    }

    /**
     * Remove the specified contact.
     */
    public function destroy(Contact $contact)
    {
        $contact->delete();

        return redirect()->route('admin.contacts.index')
            ->with('success', 'Contact deleted successfully.');
    }

    /**
     * Bulk update contact statuses.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'contact_ids' => 'required|array',
            'contact_ids.*' => 'exists:contacts,id',
            'action' => 'required|in:mark_read,mark_replied,mark_closed,delete'
        ]);

        $contacts = Contact::whereIn('id', $request->contact_ids);

        switch ($request->action) {
            case 'mark_read':
                $contacts->update([
                    'status' => Contact::STATUS_READ,
                    'read_at' => now(),
                    'read_by' => auth()->id()
                ]);
                $message = 'Contacts marked as read.';
                break;
            case 'mark_replied':
                $contacts->update(['status' => Contact::STATUS_REPLIED]);
                $message = 'Contacts marked as replied.';
                break;
            case 'mark_closed':
                $contacts->update(['status' => Contact::STATUS_CLOSED]);
                $message = 'Contacts marked as closed.';
                break;
            case 'delete':
                $contacts->delete();
                $message = 'Contacts deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }
}