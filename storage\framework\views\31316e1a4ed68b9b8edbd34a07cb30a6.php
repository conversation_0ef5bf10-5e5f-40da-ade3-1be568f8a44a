<?php $__env->startSection('title', 'Admin Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white">Admin Dashboard</h1>
        <p class="text-gray-300 mt-2">System overview and management</p>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Users Stats -->
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Users</p>
                    <p class="text-2xl font-semibold text-white"><?php echo e(number_format($stats['users']['total'])); ?></p>
                    <p class="text-sm text-green-400">+<?php echo e($stats['users']['new_this_month']); ?> this month</p>
                </div>
            </div>
        </div>

        <!-- Courses Stats -->
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Courses</p>
                    <p class="text-2xl font-semibold text-white"><?php echo e(number_format($stats['courses']['total'])); ?></p>
                    <p class="text-sm text-blue-400"><?php echo e($stats['courses']['published']); ?> published</p>
                </div>
            </div>
        </div>

        <!-- Revenue Stats -->
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Revenue</p>
                    <p class="text-2xl font-semibold text-white">$<?php echo e(number_format($stats['payments']['total_revenue'], 2)); ?></p>
                    <p class="text-sm text-green-400">$<?php echo e(number_format($stats['payments']['this_month_revenue'], 2)); ?> this month</p>
                </div>
            </div>
        </div>

        <!-- Contacts Stats -->
        <div class="dark-card rounded-lg shadow-lg p-6 card-hover">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg">
                    <svg class="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Contact Messages</p>
                    <p class="text-2xl font-semibold text-white"><?php echo e(number_format($stats['contacts']['total'])); ?></p>
                    <?php if($stats['contacts']['new'] > 0): ?>
                        <p class="text-sm text-red-400"><?php echo e($stats['contacts']['new']); ?> new messages</p>
                    <?php else: ?>
                        <p class="text-sm text-gray-500">No new messages</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Tutor Revenue Overview -->
        <div class="dark-card rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">Top Earning Instructors</h3>
                <p class="text-sm text-gray-400">Total Instructor Revenue: $<?php echo e(number_format($stats['tutor_revenue']['total_instructor_revenue'], 2)); ?></p>
            </div>
            <div class="p-6">
                <?php if(count($stats['tutor_revenue']['top_earning_instructors']) > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $stats['tutor_revenue']['top_earning_instructors']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $instructor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-white"><?php echo e($instructor['name']); ?></p>
                                    <p class="text-sm text-gray-400"><?php echo e($instructor['courses_count']); ?> courses</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold text-green-400">$<?php echo e(number_format($instructor['total_revenue'], 2)); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500">No instructor revenue data available.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Contacts -->
        <div class="dark-card rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-700 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-white">Recent Contact Messages</h3>
                <a href="<?php echo e(route('admin.contacts.index')); ?>" class="text-red-400 hover:text-red-300 text-sm font-medium">View All</a>
            </div>
            <div class="p-6">
                <?php if(count($recentActivity['recent_contacts']) > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $recentActivity['recent_contacts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border-l-4 border-<?php echo e($contact->status === 'new' ? 'red' : 'gray'); ?>-400 pl-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="font-medium text-white"><?php echo e($contact->name); ?></p>
                                        <p class="text-sm text-gray-400"><?php echo e(Str::limit($contact->subject, 50)); ?></p>
                                        <p class="text-xs text-gray-500"><?php echo e($contact->created_at->diffForHumans()); ?></p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($contact->status_badge_color); ?>">
                                        <?php echo e($contact->formatted_status); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500">No recent contact messages.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Management Links -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <a href="<?php echo e(route('admin.users.index')); ?>" class="block p-6 dark-card rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:border-red-500 group">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg group-hover:bg-red-600 transition-colors duration-300">
                    <svg class="w-6 h-6 text-blue-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-semibold text-white group-hover:text-red-400 transition-colors duration-300">Manage Users</h4>
                    <p class="text-sm text-gray-400">User accounts and roles</p>
                </div>
            </div>
        </a>

        <a href="<?php echo e(route('admin.contacts.index')); ?>" class="block p-6 dark-card rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:border-red-500 group">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg group-hover:bg-red-600 transition-colors duration-300">
                    <svg class="w-6 h-6 text-red-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-semibold text-white group-hover:text-red-400 transition-colors duration-300">Contact Messages</h4>
                    <p class="text-sm text-gray-400">Customer inquiries and support</p>
                    <?php if($stats['contacts']['new'] > 0): ?>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-600 text-white mt-1">
                            <?php echo e($stats['contacts']['new']); ?> new
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </a>

        <a href="<?php echo e(route('admin.roles.index')); ?>" class="block p-6 dark-card rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:border-red-500 group">
            <div class="flex items-center">
                <div class="p-2 bg-gray-700 rounded-lg group-hover:bg-red-600 transition-colors duration-300">
                    <svg class="w-6 h-6 text-green-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-semibold text-white group-hover:text-red-400 transition-colors duration-300">Manage Roles</h4>
                    <p class="text-sm text-gray-400">User roles and permissions</p>
                </div>
            </div>
        </a>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>