<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Contact extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'subject',
        'message',
        'status',
        'read_at',
        'read_by',
        'admin_notes'
    ];

    protected $casts = [
        'read_at' => 'datetime',
    ];

    // Status constants
    const STATUS_NEW = 'new';
    const STATUS_READ = 'read';
    const STATUS_REPLIED = 'replied';
    const STATUS_CLOSED = 'closed';

    /**
     * Get the admin who read this contact.
     */
    public function readBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'read_by');
    }

    /**
     * Scope to get new contacts.
     */
    public function scopeNew($query)
    {
        return $query->where('status', self::STATUS_NEW);
    }

    /**
     * Scope to get unread contacts.
     */
    public function scopeUnread($query)
    {
        return $query->whereIn('status', [self::STATUS_NEW]);
    }

    /**
     * Mark contact as read.
     */
    public function markAsRead($userId = null)
    {
        $this->update([
            'status' => self::STATUS_READ,
            'read_at' => now(),
            'read_by' => $userId ?? auth()->id()
        ]);
    }

    /**
     * Mark contact as replied.
     */
    public function markAsReplied()
    {
        $this->update([
            'status' => self::STATUS_REPLIED
        ]);
    }

    /**
     * Mark contact as closed.
     */
    public function markAsClosed()
    {
        $this->update([
            'status' => self::STATUS_CLOSED
        ]);
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute()
    {
        return match($this->status) {
            self::STATUS_NEW => 'bg-red-100 text-red-800',
            self::STATUS_READ => 'bg-yellow-100 text-yellow-800',
            self::STATUS_REPLIED => 'bg-blue-100 text-blue-800',
            self::STATUS_CLOSED => 'bg-green-100 text-green-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get formatted status for display
     */
    public function getFormattedStatusAttribute()
    {
        return ucfirst($this->status);
    }
}