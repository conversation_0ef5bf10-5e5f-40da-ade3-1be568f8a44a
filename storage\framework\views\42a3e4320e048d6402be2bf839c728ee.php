<?php $__env->startSection('title', ucfirst($category) . ' Courses - Escape Matrix Academy'); ?>

<?php $__env->startSection('content'); ?>
<!-- Category Hero Section -->
<section class="py-20 bg-gradient-to-br from-black via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <div class="inline-flex items-center px-4 py-2 bg-red-600/20 border border-red-600/30 rounded-full text-red-400 text-sm mb-6">
                <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                <?php echo e($courses->total()); ?> <?php echo e(Str::plural('Course', $courses->total())); ?> Found
            </div>
            
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                <?php echo e(ucfirst($category)); ?>

                <span class="text-red-500">Courses</span>
            </h1>
            
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                Master <?php echo e(strtolower($category)); ?> with our comprehensive courses designed to transform your skills and accelerate your success.
            </p>
        </div>
    </div>
</section>

<!-- Breadcrumb -->
<section class="py-6 bg-gray-900 border-b border-gray-800">
    <div class="container mx-auto px-4">
        <nav class="flex items-center space-x-2 text-sm">
            <a href="<?php echo e(route('home')); ?>" class="text-gray-400 hover:text-white transition-colors">Home</a>
            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <a href="<?php echo e(route('courses.index')); ?>" class="text-gray-400 hover:text-white transition-colors">Courses</a>
            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span class="text-white"><?php echo e(ucfirst($category)); ?></span>
        </nav>
    </div>
</section>

<!-- Courses Grid -->
<section class="py-16 bg-black">
    <div class="container mx-auto px-4">
        <?php if($courses->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden hover:border-red-500 transition-all duration-300 group">
                        <div class="relative">
                            <img src="<?php echo e($course->image_url ?: 'https://via.placeholder.com/300x200'); ?>"
                                 alt="<?php echo e($course->title); ?>"
                                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute top-4 left-4">
                                <span class="bg-red-600 text-white px-2 py-1 rounded text-sm"><?php echo e($course->category); ?></span>
                            </div>
                            <div class="absolute top-4 right-4">
                                <span class="bg-black/70 text-white px-2 py-1 rounded text-sm"><?php echo e($course->formatted_price); ?></span>
                            </div>
                            <?php if($course->featured): ?>
                                <div class="absolute bottom-4 left-4">
                                    <span class="bg-yellow-600 text-white px-2 py-1 rounded text-xs font-medium">FEATURED</span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="p-6">
                            <h3 class="text-xl font-bold text-white mb-2 group-hover:text-red-500 transition-colors"><?php echo e($course->title); ?></h3>
                            <p class="text-gray-400 mb-4 line-clamp-2"><?php echo e($course->description); ?></p>

                            <div class="flex items-center gap-4 text-sm text-gray-500 mb-4">
                                <div class="flex items-center gap-1">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <?php echo e(ucfirst($course->level)); ?>

                                </div>
                                <div class="flex items-center gap-1">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <?php echo e($course->duration); ?>

                                </div>
                                <?php if($course->instructor): ?>
                                    <div class="flex items-center gap-1">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        <?php echo e($course->instructor->name); ?>

                                    </div>
                                <?php endif; ?>
                            </div>

                            <a href="<?php echo e(route('courses.show', $course)); ?>" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors inline-block text-center">
                                Learn More
                            </a>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <?php if($courses->hasPages()): ?>
                <div class="mt-12 flex justify-center">
                    <?php echo e($courses->links()); ?>

                </div>
            <?php endif; ?>
        <?php else: ?>
            <!-- No Courses Found -->
            <div class="text-center py-16">
                <div class="text-6xl mb-6">📚</div>
                <h3 class="text-2xl font-bold text-white mb-4">No <?php echo e(ucfirst($category)); ?> Courses Found</h3>
                <p class="text-gray-400 mb-8 max-w-md mx-auto">
                    We're working on adding more <?php echo e(strtolower($category)); ?> courses. Check back soon or explore our other categories.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(route('courses.index')); ?>" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md transition-colors">
                        Browse All Courses
                    </a>
                    <a href="<?php echo e(route('contact')); ?>" class="border border-gray-600 text-gray-300 hover:bg-gray-800 px-6 py-3 rounded-md transition-colors">
                        Request Course
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Related Categories -->
<section class="py-16 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">Explore Other Categories</h2>
            <p class="text-gray-400">Discover more ways to transform your skills and knowledge</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <?php
                $allCategories = ['Marketing', 'Business', 'Finance', 'Technology', 'Design', 'Personal Development'];
                $otherCategories = array_filter($allCategories, fn($cat) => strtolower($cat) !== strtolower($category));
            ?>
            
            <?php $__currentLoopData = array_slice($otherCategories, 0, 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $otherCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('courses.category', strtolower($otherCategory))); ?>" 
                   class="bg-gray-800 border border-gray-700 rounded-lg p-4 text-center hover:border-red-500 transition-all duration-300 group">
                    <div class="text-2xl mb-2">
                        <?php switch(strtolower($otherCategory)):
                            case ('marketing'): ?>
                                📈
                                <?php break; ?>
                            <?php case ('business'): ?>
                                💼
                                <?php break; ?>
                            <?php case ('finance'): ?>
                                💰
                                <?php break; ?>
                            <?php case ('technology'): ?>
                                💻
                                <?php break; ?>
                            <?php case ('design'): ?>
                                🎨
                                <?php break; ?>
                            <?php case ('personal development'): ?>
                                🧠
                                <?php break; ?>
                            <?php default: ?>
                                📚
                        <?php endswitch; ?>
                    </div>
                    <h3 class="text-white font-medium group-hover:text-red-500 transition-colors"><?php echo e($otherCategory); ?></h3>
                </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            
            <a href="<?php echo e(route('courses.index')); ?>" 
               class="bg-red-600 hover:bg-red-700 rounded-lg p-4 text-center transition-colors">
                <div class="text-2xl mb-2">🔍</div>
                <h3 class="text-white font-medium">View All</h3>
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/courses/category.blade.php ENDPATH**/ ?>