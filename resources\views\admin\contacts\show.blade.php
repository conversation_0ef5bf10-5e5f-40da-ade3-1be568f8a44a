@extends('layouts.app')

@section('title', 'Contact Message - Admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">Contact Message Details</h1>
                <p class="text-gray-400 mt-2">View and manage contact inquiry</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('admin.contacts.index') }}" class="btn-secondary">
                    Back to Contacts
                </a>
                <a href="{{ route('admin.dashboard') }}" class="btn-primary">
                    Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Contact Details -->
        <div class="lg:col-span-2">
            <div class="bg-gray-800 rounded-lg shadow">
                <div class="p-6">
                    <!-- Contact Header -->
                    <div class="flex justify-between items-start mb-6">
                        <div>
                            <h2 class="text-xl font-semibold text-white">{{ $contact->subject }}</h2>
                            <div class="mt-2 flex items-center space-x-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $contact->status_badge_color }}">
                                    {{ $contact->formatted_status }}
                                </span>
                                <span class="text-sm text-gray-400">
                                    Received {{ $contact->created_at->format('M d, Y \\a\\t h:i A') }}
                                </span>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <form method="POST" action="{{ route('admin.contacts.destroy', $contact) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this contact?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn-danger text-sm">
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="border-t border-gray-700 pt-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-1">Name</label>
                                <p class="text-white">{{ $contact->name }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                                <p class="text-white">
                                    <a href="mailto:{{ $contact->email }}" class="text-blue-400 hover:text-blue-300">
                                        {{ $contact->email }}
                                    </a>
                                </p>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Message</label>
                            <div class="bg-gray-700 rounded-lg p-4">
                                <p class="text-white whitespace-pre-wrap">{{ $contact->message }}</p>
                            </div>
                        </div>

                        @if($contact->admin_notes)
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-300 mb-2">Admin Notes</label>
                                <div class="bg-yellow-900 border border-yellow-700 rounded-lg p-4">
                                    <p class="text-yellow-100 whitespace-pre-wrap">{{ $contact->admin_notes }}</p>
                                </div>
                            </div>
                        @endif

                        @if($contact->read_at)
                            <div class="text-sm text-gray-400">
                                <p>Read on {{ $contact->read_at->format('M d, Y \\a\\t h:i A') }}</p>
                                @if($contact->readBy)
                                    <p>Read by {{ $contact->readBy->name }}</p>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div class="lg:col-span-1">
            <!-- Status Management -->
            <div class="bg-gray-800 rounded-lg shadow mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-white mb-4">Status Management</h3>
                    <form method="POST" action="{{ route('admin.contacts.update', $contact) }}">
                        @csrf
                        @method('PATCH')
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Change Status</label>
                                <select name="status" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="new" {{ $contact->status === 'new' ? 'selected' : '' }}>New</option>
                                    <option value="read" {{ $contact->status === 'read' ? 'selected' : '' }}>Read</option>
                                    <option value="replied" {{ $contact->status === 'replied' ? 'selected' : '' }}>Replied</option>
                                    <option value="closed" {{ $contact->status === 'closed' ? 'selected' : '' }}>Closed</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Admin Notes</label>
                                <textarea name="admin_notes" rows="4" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Add internal notes about this contact...">{{ $contact->admin_notes }}</textarea>
                            </div>
                            <button type="submit" class="w-full btn-primary">
                                Update Contact
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg shadow">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-white mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="mailto:{{ $contact->email }}?subject=Re: {{ $contact->subject }}" class="w-full btn-success text-center block">
                            Reply via Email
                        </a>
                        
                        @if($contact->status !== 'read')
                            <form method="POST" action="{{ route('admin.contacts.update', $contact) }}" class="w-full">
                                @csrf
                                @method('PATCH')
                                <input type="hidden" name="status" value="read">
                                <button type="submit" class="w-full btn-warning">
                                    Mark as Read
                                </button>
                            </form>
                        @endif
                        
                        @if($contact->status !== 'replied')
                            <form method="POST" action="{{ route('admin.contacts.update', $contact) }}" class="w-full">
                                @csrf
                                @method('PATCH')
                                <input type="hidden" name="status" value="replied">
                                <button type="submit" class="w-full btn-primary">
                                    Mark as Replied
                                </button>
                            </form>
                        @endif
                        
                        @if($contact->status !== 'closed')
                            <form method="POST" action="{{ route('admin.contacts.update', $contact) }}" class="w-full">
                                @csrf
                                @method('PATCH')
                                <input type="hidden" name="status" value="closed">
                                <button type="submit" class="w-full btn-secondary">
                                    Mark as Closed
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection