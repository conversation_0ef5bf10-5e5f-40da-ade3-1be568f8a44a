@extends('layouts.app')

@section('title', 'Role Details - Admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">Role Details</h1>
                <p class="text-gray-400 mt-2">View detailed information about {{ $role->name }} role</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.roles.edit', $role) }}" class="btn-primary">
                    <i class="fas fa-edit mr-2"></i>Edit Role
                </a>
                <a href="{{ route('admin.roles.index') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Roles
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Role Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Basic Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Role Name</label>
                            <p class="text-white bg-gray-800 border border-gray-700 rounded-md px-3 py-2">{{ $role->name }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $role->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ ucfirst($role->status) }}
                            </span>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                            <p class="text-white bg-gray-800 border border-gray-700 rounded-md px-3 py-2 min-h-[80px]">
                                {{ $role->description ?: 'No description provided' }}
                            </p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Created Date</label>
                            <p class="text-white bg-gray-800 border border-gray-700 rounded-md px-3 py-2">{{ $role->created_at->format('M d, Y') }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Last Updated</label>
                            <p class="text-white bg-gray-800 border border-gray-700 rounded-md px-3 py-2">{{ $role->updated_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Permissions -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Permissions</h2>
                    
                    @if($role->permissions->count() > 0)
                        @php
                            $groupedPermissions = $role->permissions->groupBy(function($permission) {
                                return explode('.', $permission->name)[0] ?? 'general';
                            });
                        @endphp
                        
                        <div class="space-y-6">
                            @foreach($groupedPermissions as $group => $groupPermissions)
                                <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                                    <h3 class="text-lg font-medium text-white mb-4 capitalize border-b border-gray-700 pb-2">
                                        {{ str_replace('_', ' ', $group) }} Permissions
                                    </h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        @foreach($groupPermissions as $permission)
                                            <div class="flex items-start space-x-3 p-2 bg-gray-700 rounded">
                                                <div class="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                                                <div class="flex-1">
                                                    <p class="text-white text-sm font-medium">{{ $permission->name }}</p>
                                                    @if($permission->description)
                                                        <p class="text-gray-400 text-xs mt-1">{{ $permission->description }}</p>
                                                    @endif
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-key text-gray-500 text-4xl mb-4"></i>
                            <p class="text-gray-400">No permissions assigned to this role</p>
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Users with this Role -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Users with this Role</h2>
                    
                    @if($role->users->count() > 0)
                        <div class="space-y-3">
                            @foreach($role->users->take(10) as $user)
                                <div class="flex items-center justify-between p-3 bg-gray-800 border border-gray-700 rounded">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center">
                                            <span class="text-white text-sm font-semibold">{{ substr($user->name, 0, 1) }}</span>
                                        </div>
                                        <div>
                                            <p class="text-white font-medium">{{ $user->name }}</p>
                                            <p class="text-gray-400 text-sm">{{ $user->email }}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $user->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ ucfirst($user->status) }}
                                        </span>
                                        <a href="{{ route('admin.users.show', $user) }}" class="text-blue-400 hover:text-blue-300 text-sm">
                                            View
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                            
                            @if($role->users->count() > 10)
                                <div class="text-center pt-4">
                                    <p class="text-gray-400 text-sm">And {{ $role->users->count() - 10 }} more users...</p>
                                    <a href="{{ route('admin.users.index', ['role' => $role->name]) }}" class="text-blue-400 hover:text-blue-300 text-sm">
                                        View all users with this role
                                    </a>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-users text-gray-500 text-4xl mb-4"></i>
                            <p class="text-gray-400">No users assigned to this role</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Quick Stats</h2>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Total Permissions</span>
                            <span class="text-white font-semibold">{{ $role->permissions->count() }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Users Assigned</span>
                            <span class="text-white font-semibold">{{ $role->users->count() }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Active Users</span>
                            <span class="text-white font-semibold">{{ $role->users->where('status', 'active')->count() }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Role Age</span>
                            <span class="text-white font-semibold">{{ $role->created_at->diffForHumans() }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Quick Actions</h2>
                    <div class="space-y-3">
                        <a href="{{ route('admin.roles.edit', $role) }}" class="w-full btn-primary text-center">
                            <i class="fas fa-edit mr-2"></i>Edit Role
                        </a>
                        
                        @if(!in_array($role->name, ['admin', 'super-admin']))
                            <form method="POST" action="{{ route('admin.roles.toggle-status', $role) }}" class="w-full">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="w-full {{ $role->status === 'active' ? 'btn-warning' : 'btn-success' }}">
                                    <i class="fas fa-{{ $role->status === 'active' ? 'pause' : 'play' }} mr-2"></i>
                                    {{ $role->status === 'active' ? 'Disable Role' : 'Enable Role' }}
                                </button>
                            </form>
                            
                            <form method="POST" action="{{ route('admin.roles.destroy', $role) }}" class="w-full">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full btn-danger" onclick="return confirm('Are you sure you want to delete this role? This action cannot be undone and will remove this role from all users.')">
                                    <i class="fas fa-trash mr-2"></i>Delete Role
                                </button>
                            </form>
                        @else
                            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-3 py-2 rounded text-sm">
                                <i class="fas fa-shield-alt mr-2"></i>
                                System role - Cannot be modified
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Permission Categories -->
            <div class="dark-card rounded-lg shadow-lg">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Permission Categories</h2>
                    @if($role->permissions->count() > 0)
                        @php
                            $categories = $role->permissions->groupBy(function($permission) {
                                return explode('.', $permission->name)[0] ?? 'general';
                            });
                        @endphp
                        <div class="space-y-3">
                            @foreach($categories as $category => $permissions)
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-400 capitalize">{{ str_replace('_', ' ', $category) }}</span>
                                    <span class="text-white font-semibold">{{ $permissions->count() }}</span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-400 text-sm">No permissions assigned</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection