@extends('layouts.app')

@section('title', 'Edit User - Admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">Edit User</h1>
                <p class="text-gray-400 mt-2">Update user information and role assignments</p>
            </div>
            <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Back to Users
            </a>
        </div>
    </div>

    <!-- User Edit Form -->
    <div class="dark-card rounded-lg shadow-lg">
        <div class="p-6">
            <form method="POST" action="{{ route('admin.users.update', $user) }}">
                @csrf
                @method('PATCH')
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white mb-4">Basic Information</h3>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Name</label>
                            <input type="text" name="name" value="{{ old('name', $user->name) }}" class="dark-input" required>
                            @error('name')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                            <input type="email" name="email" value="{{ old('email', $user->email) }}" class="dark-input" required>
                            @error('email')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Phone (Optional)</label>
                            <input type="text" name="phone" value="{{ old('phone', $user->phone) }}" class="dark-input">
                            @error('phone')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                            <select name="status" class="dark-input" required>
                                <option value="active" {{ old('status', $user->status) === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $user->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Role Assignment -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-white mb-4">Role Assignment</h3>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Roles</label>
                            <div class="space-y-2 max-h-48 overflow-y-auto bg-gray-800 border border-gray-700 rounded-md p-3">
                                @foreach($roles as $role)
                                    <label class="flex items-center space-x-2 cursor-pointer hover:bg-gray-700 p-2 rounded">
                                        <input type="checkbox" name="roles[]" value="{{ $role->id }}" 
                                               {{ in_array($role->id, $userRoleIds) ? 'checked' : '' }}
                                               class="form-checkbox h-4 w-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500">
                                        <span class="text-white">{{ $role->name }}</span>
                                        @if($role->description)
                                            <span class="text-gray-400 text-sm">({{ $role->description }})</span>
                                        @endif
                                    </label>
                                @endforeach
                            </div>
                            @error('roles')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="bg-gray-800 border border-gray-700 rounded-md p-4">
                            <h4 class="text-sm font-medium text-gray-300 mb-2">Current User Information</h4>
                            <div class="text-sm text-gray-400 space-y-1">
                                <p><span class="font-medium">Joined:</span> {{ $user->created_at->format('M d, Y') }}</p>
                                <p><span class="font-medium">Last Login:</span> {{ $user->last_login_at ? $user->last_login_at->format('M d, Y \\a\\t h:i A') : 'Never' }}</p>
                                <p><span class="font-medium">Email Verified:</span> {{ $user->email_verified_at ? 'Yes' : 'No' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Password Reset Section -->
                <div class="mt-8 pt-6 border-t border-gray-700">
                    <h3 class="text-lg font-semibold text-white mb-4">Password Management</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">New Password (Optional)</label>
                            <input type="password" name="password" class="dark-input" placeholder="Leave blank to keep current password">
                            @error('password')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Confirm New Password</label>
                            <input type="password" name="password_confirmation" class="dark-input" placeholder="Confirm new password">
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection