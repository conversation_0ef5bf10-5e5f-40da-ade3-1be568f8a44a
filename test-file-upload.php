<?php

// Simple test script to test file upload functionality
require_once 'vendor/autoload.php';

use Illuminate\Http\UploadedFile;
use Illuminate\Http\Request;

// Create a test file
$testFile = tmpfile();
fwrite($testFile, "This is a test file content for upload testing.");
$testFilePath = stream_get_meta_data($testFile)['uri'];

// Create an UploadedFile instance
$uploadedFile = new UploadedFile(
    $testFilePath,
    'test-file.txt',
    'text/plain',
    null,
    true
);

// Test array format (how the frontend sends it)
$files = [$uploadedFile];

echo "Test file created: " . $testFilePath . "\n";
echo "File is array: " . (is_array($files) ? 'true' : 'false') . "\n";
echo "File count: " . count($files) . "\n";
echo "First file is UploadedFile: " . ($files[0] instanceof UploadedFile ? 'true' : 'false') . "\n";

// Clean up
fclose($testFile);
