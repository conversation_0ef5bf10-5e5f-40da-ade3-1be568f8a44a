@extends('layouts.app')

@section('title', 'Create User - Admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">Create New User</h1>
                <p class="text-gray-400 mt-2">Add a new user to the system with role assignments</p>
            </div>
            <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Back to Users
            </a>
        </div>
    </div>

    <!-- User Create Form -->
    <div class="dark-card rounded-lg shadow-lg">
        <div class="p-6">
            <form method="POST" action="{{ route('admin.users.store') }}">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Basic Information -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Basic Information</h3>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Full Name *</label>
                            <input type="text" name="name" value="{{ old('name') }}" class="dark-input" required placeholder="Enter full name">
                            @error('name')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                            <input type="email" name="email" value="{{ old('email') }}" class="dark-input" required placeholder="Enter email address">
                            @error('email')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                            <input type="text" name="phone" value="{{ old('phone') }}" class="dark-input" placeholder="Enter phone number (optional)">
                            @error('phone')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                            <select name="status" class="dark-input" required>
                                <option value="active" {{ old('status', 'active') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="bg-blue-900 border border-blue-700 rounded-md p-4">
                            <h4 class="text-sm font-medium text-blue-300 mb-2">Account Setup</h4>
                            <ul class="text-sm text-blue-200 space-y-1 list-disc list-inside">
                                <li>User will receive a welcome email</li>
                                <li>Password reset link will be sent</li>
                                <li>Email verification required</li>
                                <li>Assign appropriate roles for access</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Password and Role Assignment -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Security & Access</h3>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Password *</label>
                            <input type="password" name="password" class="dark-input" required placeholder="Enter secure password">
                            @error('password')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                            <p class="text-gray-400 text-xs mt-1">Minimum 8 characters with letters, numbers, and symbols.</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Confirm Password *</label>
                            <input type="password" name="password_confirmation" class="dark-input" required placeholder="Confirm password">
                        </div>
                        
                        <div>
                            <div class="flex justify-between items-center mb-4">
                                <label class="block text-sm font-medium text-gray-300">Role Assignment</label>
                                <div class="flex space-x-2">
                                    <button type="button" onclick="selectAllRoles()" class="text-xs btn-secondary px-2 py-1">
                                        Select All
                                    </button>
                                    <button type="button" onclick="deselectAllRoles()" class="text-xs btn-outline px-2 py-1">
                                        Clear All
                                    </button>
                                </div>
                            </div>
                            
                            <div class="max-h-64 overflow-y-auto bg-gray-800 border border-gray-700 rounded-md p-3">
                                @foreach($roles as $role)
                                    <label class="flex items-center space-x-3 cursor-pointer hover:bg-gray-700 p-2 rounded mb-2">
                                        <input type="checkbox" name="roles[]" value="{{ $role->id }}" 
                                               {{ in_array($role->id, old('roles', [])) ? 'checked' : '' }}
                                               class="role-checkbox form-checkbox h-4 w-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500">
                                        <div class="flex-1">
                                            <span class="text-white font-medium">{{ $role->name }}</span>
                                            @if($role->description)
                                                <p class="text-gray-400 text-xs mt-1">{{ $role->description }}</p>
                                            @endif
                                            <div class="flex items-center space-x-2 mt-1">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $role->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                    {{ ucfirst($role->status) }}
                                                </span>
                                                <span class="text-gray-400 text-xs">{{ $role->permissions->count() }} permissions</span>
                                            </div>
                                        </div>
                                    </label>
                                @endforeach
                            </div>
                            
                            @error('roles')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="bg-gray-800 border border-gray-700 rounded-md p-4">
                            <h4 class="text-sm font-medium text-gray-300 mb-2">Selected Roles Summary</h4>
                            <div class="text-sm text-gray-400">
                                <p><span class="font-medium">Roles Selected:</span> <span id="role-count">0</span></p>
                                <p class="text-xs mt-1">Users can have multiple roles. Permissions are combined from all assigned roles.</p>
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="email_verified" value="1" {{ old('email_verified') ? 'checked' : '' }} class="form-checkbox h-4 w-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-white text-sm">Mark email as verified</span>
                            </label>
                            
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="send_welcome_email" value="1" {{ old('send_welcome_email', '1') ? 'checked' : '' }} class="form-checkbox h-4 w-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-white text-sm">Send welcome email</span>
                            </label>
                            
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="force_password_reset" value="1" {{ old('force_password_reset') ? 'checked' : '' }} class="form-checkbox h-4 w-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-white text-sm">Require password change on first login</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-user-plus mr-2"></i>Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function selectAllRoles() {
    const checkboxes = document.querySelectorAll('.role-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateRoleCount();
}

function deselectAllRoles() {
    const checkboxes = document.querySelectorAll('.role-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateRoleCount();
}

function updateRoleCount() {
    const checkedBoxes = document.querySelectorAll('.role-checkbox:checked');
    document.getElementById('role-count').textContent = checkedBoxes.length;
}

// Update count when checkboxes change
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.role-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateRoleCount);
    });
    
    // Initial count
    updateRoleCount();
});
</script>
@endsection