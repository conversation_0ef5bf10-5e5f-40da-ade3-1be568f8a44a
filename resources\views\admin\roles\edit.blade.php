@extends('layouts.app')

@section('title', 'Edit Role - Admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">Edit Role</h1>
                <p class="text-gray-400 mt-2">Update role information and permission assignments</p>
            </div>
            <a href="{{ route('admin.roles.index') }}" class="btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Back to Roles
            </a>
        </div>
    </div>

    <!-- Role Edit Form -->
    <div class="dark-card rounded-lg shadow-lg">
        <div class="p-6">
            <form method="POST" action="{{ route('admin.roles.update', $role) }}">
                @csrf
                @method('PATCH')
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Basic Information -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Basic Information</h3>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Role Name</label>
                            <input type="text" name="name" value="{{ old('name', $role->name) }}" class="dark-input" required>
                            @error('name')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                            <textarea name="description" rows="4" class="dark-input" placeholder="Describe the role's purpose and responsibilities...">{{ old('description', $role->description) }}</textarea>
                            @error('description')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                            <select name="status" class="dark-input" required>
                                <option value="active" {{ old('status', $role->status) === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $role->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="bg-gray-800 border border-gray-700 rounded-md p-4">
                            <h4 class="text-sm font-medium text-gray-300 mb-2">Role Information</h4>
                            <div class="text-sm text-gray-400 space-y-1">
                                <p><span class="font-medium">Created:</span> {{ $role->created_at->format('M d, Y') }}</p>
                                <p><span class="font-medium">Last Updated:</span> {{ $role->updated_at->format('M d, Y') }}</p>
                                <p><span class="font-medium">Users with this role:</span> {{ $role->users->count() }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Permission Assignment -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Permission Assignment</h3>
                        
                        <div>
                            <div class="flex justify-between items-center mb-4">
                                <label class="block text-sm font-medium text-gray-300">Permissions</label>
                                <div class="flex space-x-2">
                                    <button type="button" onclick="selectAllPermissions()" class="text-xs btn-secondary px-2 py-1">
                                        Select All
                                    </button>
                                    <button type="button" onclick="deselectAllPermissions()" class="text-xs btn-outline px-2 py-1">
                                        Deselect All
                                    </button>
                                </div>
                            </div>
                            
                            <div class="max-h-96 overflow-y-auto bg-gray-800 border border-gray-700 rounded-md p-4">
                                @php
                                    $groupedPermissions = $permissions->groupBy(function($permission) {
                                        return explode('.', $permission->name)[0] ?? 'general';
                                    });
                                @endphp
                                
                                @foreach($groupedPermissions as $group => $groupPermissions)
                                    <div class="mb-6">
                                        <h4 class="text-sm font-semibold text-white mb-3 capitalize border-b border-gray-700 pb-2">
                                            {{ str_replace('_', ' ', $group) }} Permissions
                                        </h4>
                                        <div class="grid grid-cols-1 gap-2">
                                            @foreach($groupPermissions as $permission)
                                                <label class="flex items-center space-x-3 cursor-pointer hover:bg-gray-700 p-2 rounded">
                                                    <input type="checkbox" name="permissions[]" value="{{ $permission->id }}" 
                                                           {{ $role->permissions->contains($permission->id) ? 'checked' : '' }}
                                                           class="permission-checkbox form-checkbox h-4 w-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500">
                                                    <div class="flex-1">
                                                        <span class="text-white text-sm">{{ $permission->name }}</span>
                                                        @if($permission->description)
                                                            <p class="text-gray-400 text-xs mt-1">{{ $permission->description }}</p>
                                                        @endif
                                                    </div>
                                                </label>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            
                            @error('permissions')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="bg-gray-800 border border-gray-700 rounded-md p-4">
                            <h4 class="text-sm font-medium text-gray-300 mb-2">Current Permissions Summary</h4>
                            <div class="text-sm text-gray-400">
                                <p><span class="font-medium">Total Permissions:</span> <span id="permission-count">{{ $role->permissions->count() }}</span></p>
                                <p class="text-xs mt-1">Select permissions that users with this role should have access to.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{{ route('admin.roles.index') }}" class="btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Update Role
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updatePermissionCount();
}

function deselectAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updatePermissionCount();
}

function updatePermissionCount() {
    const checkedBoxes = document.querySelectorAll('.permission-checkbox:checked');
    document.getElementById('permission-count').textContent = checkedBoxes.length;
}

// Update count when checkboxes change
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updatePermissionCount);
    });
});
</script>
@endsection