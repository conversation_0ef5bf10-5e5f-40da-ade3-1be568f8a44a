<?php $__env->startSection('title', 'Contact Message - Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white">Contact Message Details</h1>
                <p class="text-gray-400 mt-2">View and manage contact inquiry</p>
            </div>
            <div class="flex space-x-4">
                <a href="<?php echo e(route('admin.contacts.index')); ?>" class="btn-secondary">
                    Back to Contacts
                </a>
                <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn-primary">
                    Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Contact Details -->
        <div class="lg:col-span-2">
            <div class="bg-gray-800 rounded-lg shadow">
                <div class="p-6">
                    <!-- Contact Header -->
                    <div class="flex justify-between items-start mb-6">
                        <div>
                            <h2 class="text-xl font-semibold text-white"><?php echo e($contact->subject); ?></h2>
                            <div class="mt-2 flex items-center space-x-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($contact->status_badge_color); ?>">
                                    <?php echo e($contact->formatted_status); ?>

                                </span>
                                <span class="text-sm text-gray-400">
                                    Received <?php echo e($contact->created_at->format('M d, Y \\a\\t h:i A')); ?>

                                </span>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <form method="POST" action="<?php echo e(route('admin.contacts.destroy', $contact)); ?>" class="inline" onsubmit="return confirm('Are you sure you want to delete this contact?')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn-danger text-sm">
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="border-t border-gray-700 pt-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-1">Name</label>
                                <p class="text-white"><?php echo e($contact->name); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                                <p class="text-white">
                                    <a href="mailto:<?php echo e($contact->email); ?>" class="text-blue-400 hover:text-blue-300">
                                        <?php echo e($contact->email); ?>

                                    </a>
                                </p>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Message</label>
                            <div class="bg-gray-700 rounded-lg p-4">
                                <p class="text-white whitespace-pre-wrap"><?php echo e($contact->message); ?></p>
                            </div>
                        </div>

                        <?php if($contact->admin_notes): ?>
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-300 mb-2">Admin Notes</label>
                                <div class="bg-yellow-900 border border-yellow-700 rounded-lg p-4">
                                    <p class="text-yellow-100 whitespace-pre-wrap"><?php echo e($contact->admin_notes); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($contact->read_at): ?>
                            <div class="text-sm text-gray-400">
                                <p>Read on <?php echo e($contact->read_at->format('M d, Y \\a\\t h:i A')); ?></p>
                                <?php if($contact->readBy): ?>
                                    <p>Read by <?php echo e($contact->readBy->name); ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div class="lg:col-span-1">
            <!-- Status Management -->
            <div class="bg-gray-800 rounded-lg shadow mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-white mb-4">Status Management</h3>
                    <form method="POST" action="<?php echo e(route('admin.contacts.update', $contact)); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Change Status</label>
                                <select name="status" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="new" <?php echo e($contact->status === 'new' ? 'selected' : ''); ?>>New</option>
                                    <option value="read" <?php echo e($contact->status === 'read' ? 'selected' : ''); ?>>Read</option>
                                    <option value="replied" <?php echo e($contact->status === 'replied' ? 'selected' : ''); ?>>Replied</option>
                                    <option value="closed" <?php echo e($contact->status === 'closed' ? 'selected' : ''); ?>>Closed</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Admin Notes</label>
                                <textarea name="admin_notes" rows="4" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Add internal notes about this contact..."><?php echo e($contact->admin_notes); ?></textarea>
                            </div>
                            <button type="submit" class="w-full btn-primary">
                                Update Contact
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg shadow">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-white mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="mailto:<?php echo e($contact->email); ?>?subject=Re: <?php echo e($contact->subject); ?>" class="w-full btn-success text-center block">
                            Reply via Email
                        </a>
                        
                        <?php if($contact->status !== 'read'): ?>
                            <form method="POST" action="<?php echo e(route('admin.contacts.update', $contact)); ?>" class="w-full">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <input type="hidden" name="status" value="read">
                                <button type="submit" class="w-full btn-warning">
                                    Mark as Read
                                </button>
                            </form>
                        <?php endif; ?>
                        
                        <?php if($contact->status !== 'replied'): ?>
                            <form method="POST" action="<?php echo e(route('admin.contacts.update', $contact)); ?>" class="w-full">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <input type="hidden" name="status" value="replied">
                                <button type="submit" class="w-full btn-primary">
                                    Mark as Replied
                                </button>
                            </form>
                        <?php endif; ?>
                        
                        <?php if($contact->status !== 'closed'): ?>
                            <form method="POST" action="<?php echo e(route('admin.contacts.update', $contact)); ?>" class="w-full">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <input type="hidden" name="status" value="closed">
                                <button type="submit" class="w-full btn-secondary">
                                    Mark as Closed
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/admin/contacts/show.blade.php ENDPATH**/ ?>