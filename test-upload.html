<!DOCTYPE html>
<html>
<head>
    <title>Test File Upload</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    <h1>Test File Upload</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <input type="file" name="resource_file" multiple id="fileInput">
        <button type="submit">Upload File</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('fileInput');
            
            // Add files to FormData (this mimics how the course builder sends files)
            if (fileInput.files.length > 0) {
                for (let i = 0; i < fileInput.files.length; i++) {
                    formData.append('resource_file[]', fileInput.files[i]);
                }
            }
            
            fetch('/instructor/test-file-upload', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('result').innerHTML = '<pre>Error: ' + error + '</pre>';
            });
        });
    </script>
</body>
</html>
